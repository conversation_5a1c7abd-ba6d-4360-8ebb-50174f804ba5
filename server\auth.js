import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import { getOne, run, transaction, convertBoolean, parseBoolean, getCurrentTimestamp } from './db-operations.js';

// Hash password
const hashPassword = (password) => {
  const saltRounds = 12;
  return bcrypt.hashSync(password, saltRounds);
};

// Verify password
const verifyPassword = (password, hash) => {
  return bcrypt.compareSync(password, hash);
};

// Generate JWT token
const generateToken = (userId) => {
  const payload = {
    sub: userId,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
  };

  return jwt.sign(payload, process.env.JWT_SECRET);
};

// Verify JWT token
const verifyToken = (token) => {
  try {
    return jwt.verify(token, process.env.JWT_SECRET);
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
};

// Register a new user
const registerUser = async (email, password, fullName) => {
  try {
    // Check if user already exists
    const existingUser = await getOne('SELECT * FROM users WHERE email = ?', [email]);

    if (existingUser) {
      return { error: { message: 'User already exists' } };
    }

    // Generate a new UUID for the user
    const userId = uuidv4();
    const hashedPassword = hashPassword(password);
    const timestamp = getCurrentTimestamp();

    // Create profile and user in a transaction
    const queries = [
      {
        sql: `INSERT INTO profiles (id, full_name, plan, posts_count, posts_limit, last_post_time, created_at, updated_at, is_admin)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        params: [userId, fullName, 'free', 0, 3, null, timestamp, timestamp, convertBoolean(false)]
      },
      {
        sql: `INSERT INTO users (id, email, password_hash, created_at)
              VALUES (?, ?, ?, ?)`,
        params: [userId, email, hashedPassword, timestamp]
      }
    ];

    await transaction(queries);

    // Generate token
    const token = generateToken(userId);

    // Return user data and token
    return {
      data: {
        user: {
          id: userId,
          email: email,
          profile: {
            id: userId,
            full_name: fullName,
            plan: 'free',
            posts_count: 0,
            posts_limit: 3
          }
        },
        token
      },
      error: null
    };
  } catch (error) {
    console.error('Registration error:', error);
    return { error: { message: 'Registration failed' } };
  }
};

// Login a user
const loginUser = async (email, password) => {
  try {
    // Get user by email
    const user = await getOne(`
      SELECT u.*, p.full_name, p.plan, p.posts_count, p.posts_limit, p.is_admin
      FROM users u
      JOIN profiles p ON u.id = p.id
      WHERE u.email = ?
    `, [email]);

    if (!user) {
      return { error: { message: 'Invalid email or password' } };
    }

    // Verify password
    if (!verifyPassword(password, user.password_hash)) {
      return { error: { message: 'Invalid email or password' } };
    }

    // Generate token
    const token = generateToken(user.id);

    // Return user data and token
    return {
      data: {
        user: {
          id: user.id,
          email: user.email,
          profile: {
            id: user.id,
            full_name: user.full_name,
            plan: user.plan,
            posts_count: user.posts_count,
            posts_limit: user.posts_limit,
            is_admin: parseBoolean(user.is_admin)
          }
        },
        token
      },
      error: null
    };
  } catch (error) {
    console.error('Login error:', error);
    return { error: { message: 'Login failed' } };
  }
};

// Get user profile
const getUserProfile = async (userId, forceRefresh = false) => {
  try {
    const profile = await getOne(`
      SELECT p.*, u.email
      FROM profiles p
      JOIN users u ON p.id = u.id
      WHERE p.id = ?
    `, [userId]);

    if (!profile) {
      return { error: { message: 'Profile not found' } };
    }

    // Convert boolean fields
    profile.is_admin = parseBoolean(profile.is_admin);
    if (profile.published !== undefined) {
      profile.published = parseBoolean(profile.published);
    }

    return {
      data: profile,
      error: null
    };
  } catch (error) {
    console.error('Get profile error:', error);
    return { error: { message: 'Failed to get profile' } };
  }
};

// Update user profile
const updateUserProfile = async (userId, updates) => {
  try {
    const allowedFields = [
      'full_name',
      'plan',
      'posts_count',
      'posts_limit',
      'subscription_status',
      'billing_cycle',
      'next_billing_date',
      'paypal_subscription_id',
      'paypal_order_id',
      'updated_at',
      'last_post_time',
      'is_admin'
    ];

    const fields = Object.keys(updates).filter(key => allowedFields.includes(key));

    if (fields.length === 0) {
      return { error: { message: 'No valid fields to update' } };
    }

    // Convert boolean fields
    const processedUpdates = { ...updates };
    if (processedUpdates.is_admin !== undefined) {
      processedUpdates.is_admin = convertBoolean(processedUpdates.is_admin);
    }

    // Add updated timestamp if not provided
    if (!processedUpdates.updated_at) {
      processedUpdates.updated_at = getCurrentTimestamp();
    }

    const setClause = fields.map(field => `${field} = ?`).join(', ');
    const values = fields.map(field => processedUpdates[field]);
    values.push(userId);

    const result = await run(
      `UPDATE profiles SET ${setClause} WHERE id = ?`,
      values
    );

    if (result.changes === 0) {
      return { error: { message: 'Profile not found' } };
    }

    // Get updated profile
    const updatedProfile = await getUserProfile(userId);
    return updatedProfile;
  } catch (error) {
    console.error('Update profile error:', error);
    return { error: { message: 'Failed to update profile' } };
  }
};

// Check if user has unlimited posts (for admin or special cases)
const hasUnlimitedPosts = async (userId) => {
  try {
    const profile = await getOne('SELECT is_admin FROM profiles WHERE id = ?', [userId]);
    return profile ? parseBoolean(profile.is_admin) : false;
  } catch (error) {
    console.error('Error checking unlimited posts:', error);
    return false;
  }
};

// Get rate limit based on plan
const getRateLimit = (plan) => {
  const rateLimits = {
    free: 1,    // 1 post per minute
    basic: 3,   // 3 posts per minute
    pro: 5,     // 5 posts per minute
    ultra: 5    // 5 posts per minute
  };
  return rateLimits[plan] || 1;
};

// Save a post
const savePost = async (userId, content, caption, imagePath, postId = null) => {
  try {
    const timestamp = getCurrentTimestamp();

    if (postId) {
      // Update existing post
      const result = await run(`
        UPDATE posts
        SET content = ?, caption = ?, image_url = ?, updated_at = ?
        WHERE id = ? AND user_id = ?
      `, [content, caption, imagePath, timestamp, postId, userId]);

      if (result.changes === 0) {
        return { error: 'Post not found or not owned by user' };
      }

      return { postId, updated: true };
    } else {
      // Create new post
      const newPostId = uuidv4();

      await run(`
        INSERT INTO posts (id, user_id, content, caption, image_url, created_at, updated_at, published)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [newPostId, userId, content, caption, imagePath, timestamp, timestamp, convertBoolean(false)]);

      // Update user's post count
      const profile = await getUserProfile(userId);
      if (!profile.error) {
        await updateUserProfile(userId, {
          posts_count: profile.data.posts_count + 1,
          last_post_time: timestamp
        });
      }

      return { postId: newPostId, created: true };
    }
  } catch (error) {
    console.error('Save post error:', error);
    return { error: 'Failed to save post' };
  }
};

export {
  hashPassword,
  verifyPassword,
  generateToken,
  verifyToken,
  registerUser,
  loginUser,
  getUserProfile,
  updateUserProfile,
  hasUnlimitedPosts,
  getRateLimit,
  savePost
};
